<script setup lang="ts">
import { ref } from 'vue'
import ProductPicker from '@/components/ProductPicker.vue'

interface SelectedProduct {
  id: string
  sku: string
  variant_name: string
  price: number
  quantity: number
  parent_id?: string
}

const showProductPicker = ref(false)
const selectedProducts = ref<SelectedProduct[]>([])

const openProductPicker = () => {
  showProductPicker.value = true
}

const closeProductPicker = () => {
  showProductPicker.value = false
}

const handleProductsSelected = (products: SelectedProduct[]) => {
  selectedProducts.value = products
  showProductPicker.value = false
  console.log('Selected products:', products)
}

const clearSelection = () => {
  selectedProducts.value = []
}
</script>

<template>
  <div class="p-6">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-2xl font-semibold mb-6">ProductPicker Demo</h1>
      
      <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-lg font-medium">Selected Products</h2>
          <div class="flex gap-3">
            <button
              @click="clearSelection"
              :disabled="selectedProducts.length === 0"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Clear Selection
            </button>
            <button
              @click="openProductPicker"
              class="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Open Product Picker
            </button>
          </div>
        </div>

        <div v-if="selectedProducts.length > 0" class="space-y-3">
          <div
            v-for="product in selectedProducts"
            :key="product.id"
            class="flex items-center justify-between p-3 bg-gray-50 rounded-md"
          >
            <div class="flex-1">
              <div class="font-medium">{{ product.variant_name }}</div>
              <div class="text-sm text-gray-500">SKU: {{ product.sku }}</div>
            </div>
            <div class="text-right">
              <div class="font-medium">${{ product.price.toFixed(2) }}</div>
              <div class="text-sm text-gray-500">Qty: {{ product.quantity }}</div>
            </div>
          </div>
          
          <div class="border-t pt-3">
            <div class="text-right">
              <span class="text-lg font-semibold">
                Total: ${{ selectedProducts.reduce((sum, p) => sum + (p.price * p.quantity), 0).toFixed(2) }}
              </span>
            </div>
          </div>
        </div>

        <div v-else class="text-center py-8 text-gray-500">
          No products selected. Click "Open Product Picker" to select products.
        </div>
      </div>
    </div>

    <!-- Product Picker Modal -->
    <ProductPicker
      :open="showProductPicker"
      :selected-products="selectedProducts"
      title="Select Products for Demo"
      @close="closeProductPicker"
      @confirm="handleProductsSelected"
    />
  </div>
</template>
