<script setup lang="ts">
import { onMounted, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ArrowLeftIcon, PlusIcon, TrashIcon } from "lucide-vue-next";
import ProductPicker from "@/components/ProductPicker.vue";

interface SelectedProduct {
	id: string;
	sku: string;
	variant_name: string;
	price: number;
	quantity: number;
	parent_id?: string;
}

const route = useRoute();
const router = useRouter();
const orderId = route.params.id as string;

// State
const showProductPicker = ref(false);
const selectedProducts = ref<SelectedProduct[]>([]);

// Set document title when component is mounted
onMounted(() => {
	document.title = `Edit Products - Order ${orderId}`;
});

const goBack = () => {
	router.push(`/dashboard/orders/${orderId}`);
};

const goToOrdersList = () => {
	router.push("/dashboard/orders");
};

const openProductPicker = () => {
	showProductPicker.value = true;
};

const closeProductPicker = () => {
	showProductPicker.value = false;
};

const handleProductsSelected = (products: SelectedProduct[]) => {
	selectedProducts.value = products;
	showProductPicker.value = false;
};

const removeProduct = (productId: string) => {
	selectedProducts.value = selectedProducts.value.filter(p => p.id !== productId);
};

const updateQuantity = (productId: string, quantity: number) => {
	const product = selectedProducts.value.find(p => p.id === productId);
	if (product && quantity > 0) {
		product.quantity = quantity;
	}
};

const getTotalAmount = () => {
	return selectedProducts.value.reduce((total, product) => {
		return total + (product.price * product.quantity);
	}, 0);
};
</script>

<template>
	<div>
		<!-- Header with back button -->
		<div class="flex items-center gap-4 mb-6">
			<button
				@click="goBack"
				class="flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
			>
				<ArrowLeftIcon class="w-4 h-4" />
				Back to Order
			</button>
			<h1 class="text-2xl font-semibold">Edit Products - Order {{ orderId }}</h1>
		</div>

		<!-- Order Items -->
		<div class="bg-white shadow rounded-lg">
			<div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
				<h2 class="text-lg font-medium text-gray-900">Order Items</h2>
				<button
					@click="openProductPicker"
					class="flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
				>
					<PlusIcon class="w-4 h-4" />
					Add Products
				</button>
			</div>

			<!-- Selected Products Table -->
			<div v-if="selectedProducts.length > 0" class="overflow-x-auto">
				<table class="min-w-full divide-y divide-gray-200">
					<thead class="bg-gray-50">
						<tr>
							<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								SKU
							</th>
							<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Product Name
							</th>
							<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Price
							</th>
							<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Quantity
							</th>
							<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Total
							</th>
							<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Actions
							</th>
						</tr>
					</thead>
					<tbody class="bg-white divide-y divide-gray-200">
						<tr v-for="product in selectedProducts" :key="product.id">
							<td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
								{{ product.sku }}
							</td>
							<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
								{{ product.variant_name }}
							</td>
							<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
								${{ product.price.toFixed(2) }}
							</td>
							<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
								<input
									type="number"
									min="1"
									:value="product.quantity"
									@input="updateQuantity(product.id, parseInt(($event.target as HTMLInputElement).value))"
									class="w-20 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
								/>
							</td>
							<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
								${{ (product.price * product.quantity).toFixed(2) }}
							</td>
							<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
								<button
									@click="removeProduct(product.id)"
									class="text-red-600 hover:text-red-900"
								>
									<TrashIcon class="w-4 h-4" />
								</button>
							</td>
						</tr>
					</tbody>
					<tfoot class="bg-gray-50">
						<tr>
							<td colspan="4" class="px-6 py-3 text-right text-sm font-medium text-gray-900">
								Total Amount:
							</td>
							<td class="px-6 py-3 text-sm font-bold text-gray-900">
								${{ getTotalAmount().toFixed(2) }}
							</td>
							<td></td>
						</tr>
					</tfoot>
				</table>
			</div>

			<!-- Empty State -->
			<div v-else class="px-6 py-8 text-center">
				<p class="text-gray-500 mb-4">No products added to this order yet.</p>
				<button
					@click="openProductPicker"
					class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium text-indigo-600 bg-indigo-50 border border-indigo-200 rounded-md hover:bg-indigo-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
				>
					<PlusIcon class="w-4 h-4" />
					Add Products
				</button>
			</div>

			<!-- Action Buttons -->
			<div class="px-6 py-4 border-t border-gray-200 flex justify-between">
				<button
					@click="goBack"
					class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
				>
					Back to Order Details
				</button>
				<div class="flex gap-3">
					<button
						@click="goToOrdersList"
						class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
					>
						Cancel
					</button>
					<button
						:disabled="selectedProducts.length === 0"
						class="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
					>
						Save Order Items
					</button>
				</div>
			</div>
		</div>

		<!-- Product Picker Modal -->
		<ProductPicker
			:open="showProductPicker"
			:selected-products="selectedProducts"
			@close="closeProductPicker"
			@confirm="handleProductsSelected"
		/>
	</div>
</template>
