<script setup lang="ts">
import { onMounted, ref, computed, watch } from "vue";
import { useRouter } from "vue-router";
import { ArrowLeftIcon, CalendarIcon } from "lucide-vue-next";
import LoadingState from "@/components/LoadingState.vue";
import ErrorState from "@/components/ErrorState.vue";
import ordersService from "@/services/ordersService";
import customerService, { type Customer } from "@/services/customerService";

const router = useRouter();

// Form data
const formData = ref({
	customer: "",
	customer_name: "",
	issue_date: new Date().toISOString().split("T")[0], // Today's date
	delivery_date: "",
	shipping_address: "",
	billing_address: "",
});

// State
const loading = ref(false);
const saving = ref(false);
const error = ref<string | null>(null);
const customers = ref<Customer[]>([]);
const validationErrors = ref<Record<string, string>>({});

// Set document title
onMounted(() => {
	document.title = "Create Order";
	fetchCustomers();
});

// Watch customer selection to populate addresses
watch(
	() => formData.value.customer,
	(newCustomer) => {
		if (newCustomer) {
			const selectedCustomer = customers.value.find((c) => c.name === newCustomer);
			if (selectedCustomer) {
				formData.value.customer_name = selectedCustomer.customer_name;
				formData.value.shipping_address =
					selectedCustomer.customer_primary_address || "No address on file";
				formData.value.billing_address =
					selectedCustomer.billing_address ||
					selectedCustomer.customer_primary_address ||
					"No address on file";
			}
		} else {
			formData.value.customer_name = "";
			formData.value.shipping_address = "";
			formData.value.billing_address = "";
		}
	}
);

const fetchCustomers = async () => {
	try {
		loading.value = true;
		error.value = null;

		const response = await customerService.getCustomers();
		customers.value = response.customers;
	} catch (err) {
		console.error("Error fetching customers:", err);
		error.value = "Failed to load customers";

		// Fallback to mock data if API fails
		customers.value = [
			{
				name: "CUST-001",
				customer_name: "John Doe",
				customer_type: "Individual",
				customer_primary_address: "123 Main St, New York, NY 10001",
				billing_address: "123 Main St, New York, NY 10001",
			},
			{
				name: "CUST-002",
				customer_name: "Jane Smith",
				customer_type: "Individual",
				customer_primary_address: "456 Oak Ave, Los Angeles, CA 90210",
				billing_address: "789 Pine St, Los Angeles, CA 90210",
			},
			{
				name: "CUST-003",
				customer_name: "Bob Johnson",
				customer_type: "Individual",
				customer_primary_address: "321 Elm St, Chicago, IL 60601",
			},
		];
	} finally {
		loading.value = false;
	}
};

const validateForm = (): boolean => {
	validationErrors.value = {};

	// Required field validation
	if (!formData.value.customer) {
		validationErrors.value.customer = "Customer is required";
	}

	if (!formData.value.issue_date) {
		validationErrors.value.issue_date = "Issue date is required";
	} else {
		// Date validation - issue date should be today or later
		const issueDate = new Date(formData.value.issue_date);
		const today = new Date();
		today.setHours(0, 0, 0, 0);

		if (issueDate < today) {
			validationErrors.value.issue_date = "Issue date cannot be in the past";
		}
	}

	if (!formData.value.delivery_date) {
		validationErrors.value.delivery_date = "Delivery date is required";
	} else {
		// Delivery date should be after issue date
		const issueDate = new Date(formData.value.issue_date);
		const deliveryDate = new Date(formData.value.delivery_date);

		if (deliveryDate <= issueDate) {
			validationErrors.value.delivery_date = "Delivery date must be after issue date";
		}
	}

	return Object.keys(validationErrors.value).length === 0;
};

const saveDraft = async () => {
	if (!validateForm()) {
		return;
	}

	try {
		saving.value = true;
		error.value = null;

		const orderData = {
			customer: formData.value.customer,
			customer_name: formData.value.customer_name,
			transaction_date: formData.value.issue_date,
			delivery_date: formData.value.delivery_date,
			status: "Draft",
			items: [], // Empty items array for draft
		};

		const response = await ordersService.createOrder(orderData);

		// Redirect to edit products page
		router.push(`/dashboard/orders/${response.order_id}/edit-products`);
	} catch (err) {
		console.error("Error saving draft order:", err);
		error.value = "Failed to save draft order. Please try again.";
	} finally {
		saving.value = false;
	}
};

const goBack = () => {
	router.push("/dashboard/orders");
};

const retryFetch = () => {
	error.value = null;
	fetchCustomers();
};

// Computed properties
const isFormValid = computed(() => {
	return (
		formData.value.customer &&
		formData.value.issue_date &&
		formData.value.delivery_date &&
		Object.keys(validationErrors.value).length === 0
	);
});

const minDeliveryDate = computed(() => {
	if (formData.value.issue_date) {
		const issueDate = new Date(formData.value.issue_date);
		issueDate.setDate(issueDate.getDate() + 1); // Next day after issue date
		return issueDate.toISOString().split("T")[0];
	}
	return "";
});
</script>

<template>
	<div>
		<!-- Header with back button -->
		<div class="flex items-center gap-4 mb-6">
			<button
				@click="goBack"
				class="flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
			>
				<ArrowLeftIcon class="w-4 h-4" />
				Back to Orders
			</button>
			<h1 class="text-2xl font-semibold">Create Order</h1>
		</div>

		<!-- Loading state -->
		<LoadingState v-if="loading" message="Loading customers..." />

		<!-- Error state -->
		<ErrorState v-else-if="error && !customers.length" :message="error" :retry="retryFetch" />

		<!-- Order form -->
		<div v-else class="bg-white shadow rounded-lg">
			<div class="px-6 py-4 border-b border-gray-200">
				<h2 class="text-lg font-medium text-gray-900">Order Information</h2>
			</div>

			<form @submit.prevent="saveDraft" class="px-6 py-4 space-y-6">
				<!-- Customer Selection -->
				<div>
					<label for="customer" class="block text-sm font-medium text-gray-700 mb-1">
						Customer *
					</label>
					<select
						id="customer"
						v-model="formData.customer"
						class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
						:class="{ 'border-red-500': validationErrors.customer }"
					>
						<option value="">Select a customer</option>
						<option
							v-for="customer in customers"
							:key="customer.name"
							:value="customer.name"
						>
							{{ customer.customer_name }} ({{ customer.name }})
						</option>
					</select>
					<p v-if="validationErrors.customer" class="mt-1 text-sm text-red-600">
						{{ validationErrors.customer }}
					</p>
				</div>

				<!-- Issue Date -->
				<div>
					<label for="issue_date" class="block text-sm font-medium text-gray-700 mb-1">
						Issue Date *
					</label>
					<div class="relative">
						<input
							id="issue_date"
							v-model="formData.issue_date"
							type="date"
							:min="new Date().toISOString().split('T')[0]"
							class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
							:class="{ 'border-red-500': validationErrors.issue_date }"
						/>
						<CalendarIcon
							class="absolute right-3 top-2.5 h-5 w-5 text-gray-400 pointer-events-none"
						/>
					</div>
					<p v-if="validationErrors.issue_date" class="mt-1 text-sm text-red-600">
						{{ validationErrors.issue_date }}
					</p>
				</div>

				<!-- Delivery Date -->
				<div>
					<label
						for="delivery_date"
						class="block text-sm font-medium text-gray-700 mb-1"
					>
						Delivery Date *
					</label>
					<div class="relative">
						<input
							id="delivery_date"
							v-model="formData.delivery_date"
							type="date"
							:min="minDeliveryDate"
							class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
							:class="{ 'border-red-500': validationErrors.delivery_date }"
						/>
						<CalendarIcon
							class="absolute right-3 top-2.5 h-5 w-5 text-gray-400 pointer-events-none"
						/>
					</div>
					<p v-if="validationErrors.delivery_date" class="mt-1 text-sm text-red-600">
						{{ validationErrors.delivery_date }}
					</p>
				</div>

				<!-- Primary Shipping Address -->
				<div v-if="formData.customer">
					<label
						for="shipping_address"
						class="block text-sm font-medium text-gray-700 mb-1"
					>
						Primary Shipping Address
					</label>
					<div
						class="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm text-gray-600"
					>
						{{ formData.shipping_address || "No shipping address on file" }}
					</div>
				</div>

				<!-- Primary Billing Address -->
				<div v-if="formData.customer">
					<label
						for="billing_address"
						class="block text-sm font-medium text-gray-700 mb-1"
					>
						Primary Billing Address
					</label>
					<div
						class="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm text-gray-600"
					>
						{{ formData.billing_address || "No billing address on file" }}
					</div>
				</div>

				<!-- Error message -->
				<div v-if="error" class="bg-red-50 border border-red-200 rounded-md p-4">
					<div class="flex">
						<div class="ml-3">
							<h3 class="text-sm font-medium text-red-800">Error</h3>
							<div class="mt-2 text-sm text-red-700">
								{{ error }}
							</div>
						</div>
					</div>
				</div>

				<!-- Form Actions -->
				<div class="flex justify-end gap-3 pt-4 border-t border-gray-200">
					<button
						type="button"
						@click="goBack"
						class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
					>
						Cancel
					</button>
					<button
						type="submit"
						:disabled="!isFormValid || saving"
						class="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
					>
						<span v-if="saving">Saving...</span>
						<span v-else>Save Draft</span>
					</button>
				</div>
			</form>
		</div>
	</div>
</template>
