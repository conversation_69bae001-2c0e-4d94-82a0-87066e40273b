import { fetchData, postData } from './apiService';

export interface Order {
  id: string;
  name: string;
  issue_date: string;
  customer: string;
  customer_name: string;
  delivery_date: string;
  status: string;
  amount: number;
  currency: string;
}

export interface OrdersResponse {
  orders: Order[];
  count: number;
}

export interface OrdersParams {
  search?: string;
  page?: number;
  per_page?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

/**
 * Fetches orders with optional search, pagination, and sorting
 */
export async function getOrders(params: OrdersParams = {}): Promise<OrdersResponse> {
  try {
    const response = await fetchData<{ orders: OrdersResponse }>('/orders', params);
    return response.data.orders;
  } catch (error) {
    console.error('Error fetching orders:', error);
    throw error;
  }
}

/**
 * Fetches a single order by ID
 */
export async function getOrder(id: string): Promise<Order> {
  try {
    const response = await fetchData<{ order: Order }>(`/orders/${id}`);
    return response.data.order;
  } catch (error) {
    console.error('Error fetching order:', error);
    throw error;
  }
}

/**
 * Creates a new order
 */
export async function createOrder(orderData: Partial<Order> & { items: any[] }): Promise<{ order_id: string }> {
  try {
    const response = await postData<{ order_id: string }>('/orders', orderData);
    return response.data;
  } catch (error) {
    console.error('Error creating order:', error);
    throw error;
  }
}

const ordersService = {
  getOrders,
  getOrder,
  createOrder
};

export default ordersService;
