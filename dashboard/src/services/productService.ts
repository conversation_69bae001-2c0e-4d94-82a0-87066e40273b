import { fetchData } from './apiService';

export interface Product {
  id: string;
  product_name: string;
  product_group: string;
  stock_uom: string;
  item_code: string;
  brand?: string;
  has_variants: boolean;
  variant_of?: string;
  image?: string;
  description?: string;
  tags?: string;
  price?: number;
}

export interface ProductVariant extends Product {
  variant_of: string;
  sku: string;
  variant_name: string;
  price: number;
}

export interface ProductWithVariants extends Product {
  variants: ProductVariant[];
}

export interface ProductsResponse {
  products: Product[];
  count: number;
}

export interface ProductsParams {
  search?: string;
  category?: string;
  tag?: string;
}

/**
 * Fetches products with optional search and filtering
 */
export async function getProducts(params: ProductsParams = {}): Promise<ProductsResponse> {
  try {
    const response = await fetchData<{ products: ProductsResponse }>('/products', params);
    return response.data.products;
  } catch (error) {
    console.error('Error fetching products:', error);
    throw error;
  }
}

/**
 * Fetches a single product by ID
 */
export async function getProduct(id: string): Promise<Product> {
  try {
    const response = await fetchData<{ product: Product }>(`/products/${id}`);
    return response.data.product;
  } catch (error) {
    console.error('Error fetching product:', error);
    throw error;
  }
}

/**
 * Groups products by parent and variants
 */
export function groupProductsByVariants(products: Product[]): ProductWithVariants[] {
  const parentProducts = products.filter(p => p.has_variants || !p.variant_of);
  const variants = products.filter(p => p.variant_of) as ProductVariant[];
  
  return parentProducts.map(parent => ({
    ...parent,
    variants: variants.filter(v => v.variant_of === parent.id).map(v => ({
      ...v,
      sku: v.item_code,
      variant_name: v.product_name,
      price: v.price || 0
    }))
  }));
}

const productService = {
  getProducts,
  getProduct,
  groupProductsByVariants
};

export default productService;
