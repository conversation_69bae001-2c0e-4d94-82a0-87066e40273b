import { fetchData } from './apiService';

export interface Customer {
  name: string;
  customer_name: string;
  customer_type: string;
  primary_address?: string;
  billing_address?: string;
  territory?: string;
  customer_group?: string;
  primary_contact?: string;
}

export interface CustomersResponse {
  customers: Customer[];
  count: number;
}

/**
 * Fetches customers
 */
export async function getCustomers(): Promise<CustomersResponse> {
  try {
    const response = await fetchData<{ customers: CustomersResponse }>('/customers');
    return response.data.customers;
  } catch (error) {
    console.error('Error fetching customers:', error);
    throw error;
  }
}

/**
 * Fetches a single customer by ID
 */
export async function getCustomer(id: string): Promise<Customer> {
  try {
    const response = await fetchData<{ customer: Customer }>(`/customers/${id}`);
    return response.data.customer;
  } catch (error) {
    console.error('Error fetching customer:', error);
    throw error;
  }
}

const customerService = {
  getCustomers,
  getCustomer
};

export default customerService;
