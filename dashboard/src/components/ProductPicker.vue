<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { SearchIcon, PackageIcon, CheckIcon } from 'lucide-vue-next'
import Modal from '@/components/ui/Modal.vue'
import LoadingState from '@/components/LoadingState.vue'
import ErrorState from '@/components/ErrorState.vue'
import productService, { type Product, type ProductWithVariants, type ProductVariant } from '@/services/productService'
import { useDebounce } from '@/composables/useDebounce'

interface Props {
  open: boolean
  title?: string
  selectedProducts?: SelectedProduct[]
}

interface SelectedProduct {
  id: string
  sku: string
  variant_name: string
  price: number
  quantity: number
  parent_id?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: 'Select Products',
  selectedProducts: () => []
})

const emit = defineEmits<{
  close: []
  confirm: [products: SelectedProduct[]]
}>()

// State
const loading = ref(false)
const error = ref<string | null>(null)
const searchTerm = ref('')
const products = ref<ProductWithVariants[]>([])
const selectedItems = ref<Map<string, SelectedProduct>>(new Map())

// Debounced search term (300ms delay)
const debouncedSearchTerm = useDebounce(searchTerm, 300)

// Watch for search changes
watch(debouncedSearchTerm, () => {
  fetchProducts()
})

// Initialize selected items from props
watch(() => props.selectedProducts, (newSelected) => {
  const newMap = new Map<string, SelectedProduct>()
  newSelected.forEach(item => {
    newMap.set(item.id, item)
  })
  selectedItems.value = newMap
}, { immediate: true })

onMounted(() => {
  fetchProducts()
})

const fetchProducts = async () => {
  try {
    loading.value = true
    error.value = null

    const params = debouncedSearchTerm.value ? { search: debouncedSearchTerm.value } : {}
    const response = await productService.getProducts(params)
    
    // Group products by variants
    products.value = productService.groupProductsByVariants(response.products)
  } catch (err) {
    console.error('Error fetching products:', err)
    error.value = 'Failed to load products. Please try again.'
  } finally {
    loading.value = false
  }
}

const retryFetch = () => {
  error.value = null
  fetchProducts()
}

// Check if a variant is selected
const isVariantSelected = (variantId: string): boolean => {
  return selectedItems.value.has(variantId)
}

// Check if all variants of a parent are selected
const areAllVariantsSelected = (product: ProductWithVariants): boolean => {
  if (product.variants.length === 0) return false
  return product.variants.every(variant => selectedItems.value.has(variant.id))
}

// Check if some (but not all) variants of a parent are selected
const areSomeVariantsSelected = (product: ProductWithVariants): boolean => {
  if (product.variants.length === 0) return false
  const selectedCount = product.variants.filter(variant => selectedItems.value.has(variant.id)).length
  return selectedCount > 0 && selectedCount < product.variants.length
}

// Toggle variant selection
const toggleVariant = (variant: ProductVariant, parentId: string) => {
  const newMap = new Map(selectedItems.value)
  
  if (newMap.has(variant.id)) {
    newMap.delete(variant.id)
  } else {
    newMap.set(variant.id, {
      id: variant.id,
      sku: variant.sku,
      variant_name: variant.variant_name,
      price: variant.price,
      quantity: 1,
      parent_id: parentId
    })
  }
  
  selectedItems.value = newMap
}

// Toggle all variants of a parent product
const toggleAllVariants = (product: ProductWithVariants) => {
  const newMap = new Map(selectedItems.value)
  const allSelected = areAllVariantsSelected(product)
  
  product.variants.forEach(variant => {
    if (allSelected) {
      newMap.delete(variant.id)
    } else {
      newMap.set(variant.id, {
        id: variant.id,
        sku: variant.sku,
        variant_name: variant.variant_name,
        price: variant.price,
        quantity: 1,
        parent_id: product.id
      })
    }
  })
  
  selectedItems.value = newMap
}

// Update quantity for a selected variant
const updateQuantity = (variantId: string, quantity: number) => {
  const item = selectedItems.value.get(variantId)
  if (item && quantity > 0) {
    selectedItems.value.set(variantId, { ...item, quantity })
  }
}

// Get selected products as array
const selectedProductsArray = computed(() => {
  return Array.from(selectedItems.value.values())
})

// Handle confirm action
const handleConfirm = () => {
  emit('confirm', selectedProductsArray.value)
}

// Handle close action
const handleClose = () => {
  emit('close')
}
</script>

<template>
  <Modal
    :open="open"
    :title="title"
    max-width="5xl"
    @close="handleClose"
  >
    <!-- Search Bar -->
    <div class="mb-6">
      <div class="relative">
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <SearchIcon class="h-5 w-5 text-gray-400" />
        </div>
        <input
          v-model="searchTerm"
          type="text"
          placeholder="Search products..."
          class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
        />
      </div>
    </div>

    <!-- Content -->
    <div class="max-h-96 overflow-y-auto">
      <!-- Loading State -->
      <LoadingState v-if="loading" message="Loading products..." />

      <!-- Error State -->
      <ErrorState v-else-if="error" :message="error" :retry="retryFetch" />

      <!-- Products List -->
      <div v-else-if="products.length > 0" class="space-y-4">
        <div
          v-for="product in products"
          :key="product.id"
          class="border border-gray-200 rounded-lg p-4"
        >
          <!-- Parent Product Header -->
          <div class="flex items-center gap-3 mb-3">
            <input
              type="checkbox"
              :checked="areAllVariantsSelected(product)"
              :indeterminate="areSomeVariantsSelected(product)"
              @change="toggleAllVariants(product)"
              class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
            />
            <PackageIcon class="h-5 w-5 text-gray-400" />
            <div class="flex-1">
              <h3 class="text-sm font-medium text-gray-900">{{ product.product_name }}</h3>
              <p class="text-xs text-gray-500">{{ product.product_group }}</p>
            </div>
            <span class="text-xs text-gray-500">
              {{ product.variants.length }} variant{{ product.variants.length !== 1 ? 's' : '' }}
            </span>
          </div>

          <!-- Variants -->
          <div v-if="product.variants.length > 0" class="ml-7 space-y-2">
            <div
              v-for="variant in product.variants"
              :key="variant.id"
              class="flex items-center gap-3 p-2 bg-gray-50 rounded"
            >
              <input
                type="checkbox"
                :checked="isVariantSelected(variant.id)"
                @change="toggleVariant(variant, product.id)"
                class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              />
              <div class="flex-1 grid grid-cols-3 gap-4 text-sm">
                <div>
                  <span class="font-medium">{{ variant.sku }}</span>
                </div>
                <div>
                  <span>{{ variant.variant_name }}</span>
                </div>
                <div>
                  <span class="text-gray-600">${{ variant.price.toFixed(2) }}</span>
                </div>
              </div>
              <div v-if="isVariantSelected(variant.id)" class="w-20">
                <input
                  type="number"
                  min="1"
                  :value="selectedItems.get(variant.id)?.quantity || 1"
                  @input="updateQuantity(variant.id, parseInt(($event.target as HTMLInputElement).value))"
                  class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-else class="text-center py-8">
        <PackageIcon class="mx-auto h-12 w-12 text-gray-400" />
        <h3 class="mt-2 text-sm font-medium text-gray-900">No products found</h3>
        <p class="mt-1 text-sm text-gray-500">
          {{ searchTerm ? 'Try adjusting your search terms' : 'No products available' }}
        </p>
      </div>
    </div>

    <!-- Footer -->
    <template #footer>
      <button
        @click="handleClose"
        class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
      >
        Cancel
      </button>
      <button
        @click="handleConfirm"
        :disabled="selectedProductsArray.length === 0"
        class="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        Confirm ({{ selectedProductsArray.length }})
      </button>
    </template>
  </Modal>
</template>
