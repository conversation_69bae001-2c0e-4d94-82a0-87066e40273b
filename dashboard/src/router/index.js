import { createRouter, createWebHistory } from "vue-router";
import DashboardLayout from "@/layouts/DashboardLayout.vue";
import { useAuthStore } from "../stores/auth";

const routes = [
	{ path: "/", redirect: "/dashboard" },
	{
		path: "/login",
		redirect: "/dashboard/login",
	},
	{
		path: "/dashboard/login",
		name: "login",
		component: () => import("../views/LoginView.vue"),
		meta: {
			requiresAuth: false,
			guestOnly: true,
		},
	},
	{
		path: "/dashboard",
		component: DashboardLayout,
		meta: {
			requiresAuth: true,
		},
		children: [
			{
				path: "",
				name: "dashboard",
				component: () => import("@/views/Dashboard/DashboardIndex.vue"),
			},
			{
				path: "products",
				name: "products",
				component: () => import("@/views/Products/ProductList.vue"),
			},
			{
				path: "products/new",
				name: "new-product",
				component: () => import("@/views/Products/NewProduct.vue"),
			},
			{
				path: "products/picker-demo",
				name: "product-picker-demo",
				component: () => import("@/views/Products/ProductPickerDemo.vue"),
			},
			{
				path: "categories",
				name: "categories",
				component: () => import("@/views/Categories/CategoryList.vue"),
			},
			{
				path: "categories/new",
				name: "new-category",
				component: () => import("@/views/Categories/NewCategory.vue"),
			},
			{
				path: "collections",
				name: "collections",
				component: () => import("@/views/Collections/CollectionList.vue"),
			},
			{
				path: "collections/new",
				name: "new-collection",
				component: () => import("@/views/Collections/NewCollection.vue"),
			},

			// Orders
			{
				path: "orders",
				name: "orders",
				component: () => import("@/views/Orders/OrderList.vue"),
			},
			{
				path: "orders/new",
				name: "new-order",
				component: () => import("@/views/Orders/OrderCreate.vue"),
			},
			{
				path: "orders/:id",
				name: "order-details",
				component: () => import("@/views/Orders/OrderDetail.vue"),
			},
			{
				path: "orders/:id/edit-products",
				name: "edit-order-products",
				component: () => import("@/views/Orders/OrderEditProducts.vue"),
			},
			// Customers
			{
				path: "customers",
				name: "customers",
				component: () => import("@/views/Customers/CustomerList.vue"),
			},
			{
				path: "customers/:id",
				name: "customer-details",
				component: () => import("@/views/Customers/CustomerDetails.vue"),
			},
			{
				path: "customers/new",
				name: "new-customer",
				component: () => import("@/views/Customers/NewCustomer.vue"),
			},
			{
				path: "customer-groups",
				name: "customer-groups",
				component: () => import("@/views/CustomerGroups/CustomerGroupList.vue"),
			},
			{
				path: "customer-groups/:id",
				name: "customer-group-details",
				component: () => import("@/views/CustomerGroups/CustomerGroupDetails.vue"),
			},
			{
				path: "customer-groups/new",
				name: "new-customer-groups",
				component: () => import("@/views/CustomerGroups/NewCustomerGroup.vue"),
			},
		],
	},
	{
		path: "/:pathMatch(.*)*",
		name: "not-found",
		component: () => import("../views/NotFoundView.vue"),
	},
];

const router = createRouter({
	history: createWebHistory(""),
	routes,
});

// Navigation guard
router.beforeEach(async (to, from, next) => {
	const authStore = useAuthStore();

	if (to.name === "login") {
		authStore.clearAuthData();
		return next();
	}

	try {
		if (to.matched.some((record) => record.meta.requiresAuth)) {
			// Check authentication status with server validation
			const isAuthenticated = await authStore.checkAuth();

			if (!isAuthenticated) {
				console.log("[Router] Authentication failed, redirecting to login");
				return next({ name: "login" });
			}
		}
	} catch (err) {
		console.error("Auth check failed:", err);
		authStore.clearAuthData();
		return next({ name: "login" });
	}

	next();
});

export default router;
