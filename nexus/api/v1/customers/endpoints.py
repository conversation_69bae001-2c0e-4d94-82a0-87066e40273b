import frappe
import iam
from frappe.rate_limiter import rate_limit
from iam.utils.validate.api import validate_required_parameters
from nexus.api.v1.customers import schemas


@iam.whitelist(require_auth=True)
@rate_limit(**iam.get_api_rate_limit())
def get_customers(**parameters):
	"""
	Get all customers with their primary addresses
	"""
	parameters.pop("cmd")
	customers = iam.db.get(**schemas.GET_CUSTOMERS, **parameters)

	# Fetch address details for each customer
	for customer in customers:
		if customer.get("primary_address"):
			try:
				addresses = iam.db.get(
					customer_primary_address=customer["primary_address"],
					**schemas.GET_CUSTOMER_ADDRESS
				)
				if addresses:
					address = addresses[0]
					# Format the address as a readable string
					address_parts = []
					if address.get("address_line1"):
						address_parts.append(address["address_line1"])
					if address.get("address_line2"):
						address_parts.append(address["address_line2"])
					if address.get("city"):
						address_parts.append(address["city"])
					if address.get("state"):
						address_parts.append(address["state"])
					if address.get("country"):
						address_parts.append(address["country"])
					if address.get("pincode"):
						address_parts.append(address["pincode"])

					customer["primary_address"] = ", ".join(address_parts) if address_parts else "No address on file"
					customer["billing_address"] = customer["primary_address"]  # Use same address for billing by default
				else:
					customer["primary_address"] = "No address on file"
					customer["billing_address"] = "No address on file"
			except Exception as e:
				print(f"Error fetching address for customer {customer.get('name')}: {e}")
				customer["primary_address"] = "No address on file"
				customer["billing_address"] = "No address on file"
		else:
			customer["primary_address"] = "No address on file"
			customer["billing_address"] = "No address on file"

	count = len(customers)
	data = {"customers": customers, "count": count}

	schemas.CustomersRetrieved(data={"customers": data})


@iam.whitelist(require_auth=True)
@rate_limit(**iam.get_api_rate_limit())
def get_customer(**parameters):
	"""
	Get a specific customer
	"""
	expected_parameters = required_parameters = ["customer_id"]
	parameters = iam.db.prepare_query_parameters(expected_parameters, parameters)
	validate_required_parameters(parameters, required_parameters)

	customer = iam.db.get(**schemas.GET_CUSTOMER)[0]
	addresses = iam.db.get(customer_primary_address=customer.customer_primary_address, **schemas.GET_CUSTOMER_ADDRESS)
	customer["address"] = addresses[0] if addresses else {}
	data = {"customer": customer}

	schemas.CustomerRetrieved(data={"customer": data})


@iam.whitelist(require_auth=True)
@rate_limit(**iam.get_api_rate_limit())
def create_customer(**parameters):
	expected_parameters = schemas.CREATE_CUSTOMER.get("expected_parameters")
	required_parameters = schemas.CREATE_CUSTOMER.get("required_parameters")
	parameters = iam.db.prepare_query_parameters(expected_parameters, parameters)
	validate_required_parameters(parameters, required_parameters)

	customer = frappe.new_doc("Customer")
	customer.update(parameters)
	customer.save()

	schemas.CustomerCreated()


@iam.whitelist(require_auth=True)
@rate_limit(**iam.get_api_rate_limit())
def update_customer(**parameters):
	"""
	Update a specific customer
	"""
	expected_parameters = schemas.UPDATE_CUSTOMER.get("expected_parameters")
	required_parameters = schemas.UPDATE_CUSTOMER.get("required_parameters")
	parameters = iam.db.prepare_query_parameters(expected_parameters, parameters)
	validate_required_parameters(parameters, required_parameters)

	customer_id = parameters.pop("customer_id")

	# Extract address-related parameters
	address_fields = ["address_line1", "address_line2", "city", "state", "country", "pincode"]
	address_parameters = {key: parameters.pop(key) for key in address_fields if key in parameters}

	# Update Customer document
	customer = frappe.get_doc("Customer", customer_id)
	customer.update(parameters)
	customer.save()

	# Update Address document if address parameters were provided
	if address_parameters and customer.customer_primary_address:
		address = frappe.get_doc("Address", customer.customer_primary_address)
		address.update(address_parameters)
		address.save()

	schemas.CustomerUpdated()
