import iam

GET_CUSTOMERS = {
	"doctype": "Customer",
	"select_fields": {
		"Customer": [
			("name", "name"),
			("customer_name", "customer_name"),
			("customer_group", "customer_group"),
			("customer_type", "customer_type"),
			("customer_primary_address", "primary_address"),
			("customer_primary_contact", "primary_contact"),
			("territory", "territory"),
		]
	},
	"conditions": {
		"customer_group": {"doctype": "Customer", "field": "customer_group", "operator": "=="},
		"search": {"doctype": "Customer", "field": "customer_name", "operator": "like"},
	},
}


class CustomersRetrieved(iam.Success):
	specific_code = "200"
	message = "All customers are retrieved successfully."


GET_CUSTOMER = {
	"doctype": "Customer",
	"select_fields": {
		"Customer": [
			("name", "id"),
			("customer_name", "customer_name"),
			("customer_group", "customer_group"),
			("customer_type", "customer_type"),
			("territory", "territory"),
			("lead_name", "lead_name"),
			("opportunity_name", "opportunity_name"),
			("prospect_name", "prospect_name"),
			("account_manager", "account_manager"),
			("email_id", "email_id"),
			("mobile_no", "mobile_no"),
			("customer_primary_address", "customer_primary_address"),
		]
	},
	"conditions": {
		"customer_id": {"doctype": "Customer", "field": "name", "operator": "=="},
	},
}

GET_CUSTOMER_ADDRESS = {
	"doctype": "Address",
	"select_fields": {
		"Address": [
			("name", "address_id"),
			("address_line1", "address_line1"),
			("address_line2", "address_line2"),
			("city", "city"),
			("state", "state"),
			("country", "country"),
			("pincode", "pincode"),
			("address_type", "address_type"),
		]
	},
	"conditions": {
		"customer_primary_address": {"doctype": "Address", "field": "name", "operator": "=="},
	},
}


class CustomerRetrieved(iam.Success):
	specific_code = "200"
	message = "Customer is retrieved successfully."


CREATE_CUSTOMER = {
	"required_parameters": [
		"customer_name",
		"customer_type",
		"email_id",
		"mobile_no",
		"address_line1",
		"city",
		"state",
		"country",
		"pincode",
	],
	"expected_parameters": [
		"customer_name",
		"customer_type",
		"customer_group",
		"email_id",
		"mobile_no",
		"address_line1",
		"address_line2",
		"city",
		"state",
		"country",
		"pincode",
	],
}


class CustomerCreated(iam.Success):
	specific_code = "201"
	message = "Customer is created successfully."


UPDATE_CUSTOMER = {
	"required_parameters": [
		"customer_id",
	],
	"expected_parameters": [
		"customer_id",
		"customer_name",
		"customer_type",
		"customer_group",
		"email_id",
		"mobile_no",
		"address_line1",
		"address_line2",
		"city",
		"state",
		"country",
		"pincode",
	],
}


class CustomerUpdated(iam.Success):
	specific_code = "200"
	message = "Customer is updated successfully."
