import frappe
import iam
from frappe.rate_limiter import rate_limit
from iam.utils.validate.api import validate_required_parameters

from nexus.api.v1.orders import schemas


@iam.whitelist(require_auth=True)
@rate_limit(**iam.get_api_rate_limit())
def get_orders(**parameters):
	"""
	Get all orders with optional filtering
	"""
	parameters.pop("cmd")
	expected_parameters = schemas.GET_ORDERS.get("expected_parameters")
	parameters = iam.db.prepare_query_parameters(expected_parameters, parameters)
	search = parameters.pop("search", None)
	if search:
		parameters["search_name"] = parameters["search_customer_name"] = search

	orders = iam.db.get(**schemas.GET_ORDERS, **parameters)
	count = len(orders)
	data = {"orders": orders, "count": count}

	schemas.OrdersRetrieved(data={"orders": data})


@iam.whitelist(require_auth=True)
@rate_limit(**iam.get_api_rate_limit())
def get_order(order_id, **parameters):
	"""
	Get a single order by ID
	"""
	parameters.pop("cmd", None)
	order_data = iam.db.get(**schemas.GET_ORDER, order_id=order_id)
	if not order_data:
		schemas.OrderNotFound()
		return
	order = order_data[0] if order_data else None
	if not order:
		schemas.OrderNotFound()
		return
	schemas.OrderRetrieved(data={"order": order})


@iam.whitelist(require_auth=True)
@rate_limit(**iam.get_api_rate_limit())
def create_order(**parameters):
	"""
	Create a new sales order
	"""
	try:
		parameters.pop("cmd", None)
		expected_parameters = schemas.CREATE_ORDER.get("expected_parameters")
		required_parameters = schemas.CREATE_ORDER.get("required_parameters")
		parameters = iam.db.prepare_query_parameters(expected_parameters, parameters)
		validate_required_parameters(parameters, required_parameters)

		# Extract items from parameters
		items = parameters.pop("items", [])

		# Create new Sales Order
		order = frappe.new_doc("Sales Order")

		# Set basic fields
		order.customer = parameters.get("customer")
		order.customer_name = parameters.get("customer_name", parameters.get("customer"))
		order.transaction_date = parameters.get("transaction_date")
		order.delivery_date = parameters.get("delivery_date")
		order.currency = parameters.get("currency", "USD")
		order.company = parameters.get("company", frappe.defaults.get_user_default("Company"))
		order.territory = parameters.get("territory", "All Territories")
		order.order_type = parameters.get("order_type", "Sales")
		order.po_no = parameters.get("po_no")
		order.po_date = parameters.get("po_date")

		# Add items to the order
		for item in items:
			order.append(
				"items",
				{
					"item_code": item.get("item_code"),
					"item_name": item.get("item_name", item.get("item_code")),
					"qty": item.get("qty", 1),
					"rate": item.get("rate", 0),
					"amount": item.get("amount", item.get("qty", 1) * item.get("rate", 0)),
					"delivery_date": item.get("delivery_date", parameters.get("delivery_date")),
				},
			)

		order.save()

		schemas.OrderCreated(data={"order_id": order.name})

	except Exception as e:
		frappe.log_error(f"Error creating order: {e}")
		raise e
