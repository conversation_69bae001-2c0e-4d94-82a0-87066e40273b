import iam

GET_ORDERS = {
	"doctype": "Sales Order",
	"select_fields": {
		"Sales Order": [
			("name", "id"),
			("transaction_date", "issue_date"),
			("customer", "customer"),
			("customer_name", "customer_name"),
			("delivery_date", "delivery_date"),
			("status", "status"),
			("grand_total", "amount"),
			("currency", "currency"),
			("company", "company"),
			("territory", "territory"),
			("order_type", "order_type"),
		]
	},
	"conditions": {
		"search_name": {"doctype": "Sales Order", "field": "name", "operator": "like"},
		"search_customer_name": {"doctype": "Sales Order", "field": "customer_name", "operator": "like"},
		"status": {"doctype": "Sales Order", "field": "status", "operator": "=="},
		"customer": {"doctype": "Sales Order", "field": "customer", "operator": "=="},
	},
	"expected_parameters": ["search_name", "search_customer_name", "status", "customer"],
}

GET_ORDER = {
	"doctype": "Sales Order",
	"select_fields": {
		"Sales Order": [
			("name", "id"),
			("transaction_date", "issue_date"),
			("customer", "customer"),
			("customer_name", "customer_name"),
			("delivery_date", "delivery_date"),
			("status", "status"),
			("grand_total", "amount"),
			("currency", "currency"),
			("company", "company"),
			("territory", "territory"),
			("order_type", "order_type"),
			("total_qty", "total_qty"),
			("total", "total"),
			("total_taxes_and_charges", "total_taxes_and_charges"),
			("advance_paid", "advance_paid"),
			("po_no", "po_no"),
			("po_date", "po_date"),
		]
	},
	"conditions": {
		"order_id": {"doctype": "Sales Order", "field": "name", "operator": "=="},
	},
	"expected_parameters": ["order_id"],
	"required_parameters": ["order_id"],
}

CREATE_ORDER = {
	"required_parameters": [
		"customer",
		"transaction_date",
		"delivery_date",
		"items",
	],
	"expected_parameters": [
		"customer",
		"customer_name",
		"transaction_date",
		"delivery_date",
		"currency",
		"company",
		"territory",
		"order_type",
		"po_no",
		"po_date",
		"items",
	],
}


class OrdersRetrieved(iam.Success):
	specific_code = "2000-2-1"
	message = "All orders are retrieved successfully."


class OrderRetrieved(iam.Success):
	specific_code = "2000-2-2"
	message = "Order retrieved successfully."


class OrderCreated(iam.Created):
	specific_code = "2000-2-3"
	message = "Order created successfully."


class OrderNotFound(iam.NotFoundError):
	specific_code = "2000-2-4"
	message = "Order not found."
